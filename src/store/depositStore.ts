import { makeAutoObservable, reaction } from 'mobx'
import { Network } from '@/types/network.ts'
import { networks } from '@/config/network.ts'
import Token from '@/types/token'
import { rootStore } from '@/store/index.ts'
import { readContracts, writeContract } from '@wagmi/core'
import { wagmiAdapter } from '@/provider.tsx'
import { CashierAbi } from '@/config/abi/CashierAbi.ts'
import { ERC20ABI } from '@/config/abi/Erc20Abi.ts'
import { maxUint256 } from 'viem'
import toast from 'react-hot-toast'
import { BigIntState } from '@/store/standard/BigIntState.ts'

export class DepositStore {
  fromNetwork: Network
  destNetwork: Network

  receiptAddress: string | undefined

  selectedToken: Token | undefined

  inputAmount: BigIntState = new BigIntState({})

  isReceiptAddressValid: boolean = true

  // Loading states
  isApproving: boolean = false

  constructor() {
    this.fromNetwork = networks[0]
    this.destNetwork = this.fromNetwork.destNetworks[0]

    makeAutoObservable(this)

    this.watch()
  }

  private watch() {
    reaction(
      () => ({
        fromNetwork: this.fromNetwork,
        destNetwork: this.destNetwork,
      }),
      async ({ fromNetwork, destNetwork }) => {
        if (fromNetwork && destNetwork) {
          this.fetchDepositFee()

          await rootStore.tokenStore.getTokenList(fromNetwork.chainId, destNetwork.chainId)
          if (rootStore.tokenStore.tokenList.length > 0) {
            this.setSelectedToken(rootStore.tokenStore.tokenList[0])
          }
        }
      },
      {
        fireImmediately: true,
      }
    )

    // 监听选中代币变化，自动更新 inputAmount 的小数位数
    reaction(
      () => this.selectedToken,
      selectedToken => {
        if (selectedToken && selectedToken.decimals !== undefined) {
          this.inputAmount.setDecimal(selectedToken.decimals)
        }
      },
      {
        fireImmediately: true,
      }
    )
  }

  async fetchDepositFee() {
    try {
      if (this.fromNetwork.chainId !== 4689) return

      // Filter networks that have crossOptions with cashier addresses
      const crossNetworks = this.fromNetwork.destNetworks.filter(
        network => network.crossOptions?.cashier && network.crossOptions.cashier !== '0x'
      )

      crossNetworks.forEach(network => {
        network.crossOptions?.depositFee?.setDecimal(this.fromNetwork.nativeToken.decimal)
      })

      if (crossNetworks.length === 0) {
        console.warn('No networks with valid crossOptions found for', this.fromNetwork.name)
        return
      }

      console.log(`Fetching deposit fees for ${crossNetworks.length} networks from ${this.fromNetwork.name}`)

      // Create contract calls for batch reading
      const contracts = crossNetworks.map(network => ({
        address: network.crossOptions!.cashier as `0x${string}`,
        abi: CashierAbi.Functions.depositFee,
        functionName: 'depositFee' as const,
        chainId: this.fromNetwork.chainId,
      }))

      // Batch read all depositFee values
      const results = await readContracts(wagmiAdapter.wagmiConfig, {
        contracts,
      })

      // Process results and update crossOptions
      results.forEach((result, index) => {
        const network = crossNetworks[index]
        if (result.status === 'success' && result.result && network.crossOptions) {
          // Set the depositFee value in the network's crossOptions
          network.crossOptions.depositFee.setValue(result.result as bigint)
          console.log(`✓ Updated depositFee for ${network.name}: ${result.result.toString()}`)
        } else if (result.status === 'failure') {
          console.error(`✗ Failed to fetch depositFee for ${network.name}:`, result.error)
        }
      })
    } catch (error) {
      console.error('Error fetching deposit fees:', error)
      throw error // Re-throw to allow caller to handle
    }
  }

  async approve(): Promise<string> {
    try {
      // Set loading state
      this.isApproving = true

      // Validate prerequisites
      this.validateApprovalPrerequisites()

      const token = this.selectedToken!

      const spenderAddress = token.getSpenderAddress()
      if (!spenderAddress) {
        throw new Error(`代币 ${token.symbol} 返回了空的授权地址`)
      }

      console.log(`Approving ${token.symbol} for spender: ${spenderAddress}`)

      // Execute approval transaction
      const hash = await writeContract(wagmiAdapter.wagmiConfig, {
        address: token.address as `0x${string}`,
        abi: ERC20ABI.Functions.approve,
        functionName: 'approve',
        args: [spenderAddress as `0x${string}`, maxUint256],
        chainId: this.fromNetwork.chainId,
      })

      console.log(`✓ Approval transaction submitted: ${hash}`)
      toast.success(`Approval transaction submitted successfully`)

      return hash
    } catch (error: any) {
      console.error('Approval failed:', error)
      this.handleApprovalError(error)
      throw error
    } finally {
      this.isApproving = false
    }
  }

  private validateApprovalPrerequisites(): void {
    // Check wallet connection
    if (!rootStore.accountStore.isWalletConnected) {
      throw new Error('钱包未连接，请先连接钱包')
    }

    // Check if token is selected
    if (!this.selectedToken) {
      throw new Error('请先选择要授权的代币')
    }

    // Check if token has valid address
    if (!this.selectedToken.address) {
      throw new Error('代币地址无效')
    }

    // Check if networks are properly configured
    if (!this.fromNetwork || !this.destNetwork) {
      throw new Error('网络配置无效')
    }

    // Check if user is on the correct network
    if (!rootStore.accountStore.isOnChain(this.fromNetwork.chainId)) {
      throw new Error(`请切换到 ${this.fromNetwork.name} 网络`)
    }
  }

  private handleApprovalError(error: any): void {
    let errorMessage = '授权失败'

    if (error.message) {
      if (error.message.includes('User rejected')) {
        errorMessage = '用户取消了授权操作'
      } else if (error.message.includes('insufficient funds')) {
        errorMessage = '余额不足，无法支付交易费用'
      } else if (error.message.includes('network')) {
        errorMessage = '网络错误，请检查网络连接'
      } else if (error.message.includes('钱包未连接')) {
        errorMessage = error.message
      } else if (error.message.includes('请切换到')) {
        errorMessage = error.message
      } else if (error.message.includes('无法确定授权地址')) {
        errorMessage = error.message
      } else {
        errorMessage = `授权失败: ${error.message}`
      }
    }

    toast.error(errorMessage)
  }

  async depositTo() {}

  setFromNetwork(network: Network) {
    this.fromNetwork = network
    this.destNetwork = network.destNetworks[0]
  }

  setDestNetwork(network: Network) {
    this.destNetwork = network
  }

  swapNetworks() {
    const temp = this.fromNetwork
    this.fromNetwork = this.destNetwork
    this.destNetwork = temp
  }

  setReceiptAddress(address: string | undefined) {
    this.receiptAddress = address
  }

  setSelectedToken(token: Token) {
    this.selectedToken = token
  }

  setInputAmount(amount: string) {
    this.inputAmount.setRawAmount(amount)
  }

  setReceiptAddressValid(isValid: boolean) {
    this.isReceiptAddressValid = isValid
  }

  setApproving(isApproving: boolean) {
    this.isApproving = isApproving
  }

  /**
   * 获取用户输入金额对应的 bigint 值
   * 直接从 BigIntState 获取，已经处理了精度和转换
   * @returns bigint 类型的金额值
   */
  get inputAmountValue(): bigint {
    return this.inputAmount.value
  }

  /**
   * 检查输入金额是否有效
   * @returns boolean 是否有效
   */
  get isInputAmountValid(): boolean {
    if (!this.selectedToken) {
      return false
    }

    // 检查是否有原始输入或有效值
    const hasValidInput = this.inputAmount.hasRawInput || this.inputAmount.value > 0n
    if (!hasValidInput) {
      return false
    }

    return this.inputAmount.value < this.selectedToken?.balance?.value
  }

  /**
   * 检查输入金额是否超过余额
   * @returns boolean 是否超过余额
   */
  get isAmountExceedsBalance(): boolean {
    if (!this.isInputAmountValid || !this.selectedToken) {
      return false
    }

    const inputValue = this.inputAmountValue
    const balance = this.selectedToken.balance.value

    return inputValue > balance
  }

  /**
   * 获取用于显示的输入金额字符串
   * @returns 显示用的金额字符串
   */
  get displayInputAmount(): string {
    return this.inputAmount.displayAmount
  }

  /**
   * 检查当前选中的代币是否需要 approve
   * @returns boolean 是否需要 approve
   */
  get needsApproval(): boolean {
    // 如果没有选中代币，不需要 approve
    if (!this.selectedToken) {
      return false
    }

    // 如果是原生代币，不需要 approve
    if (this.selectedToken.isNative) {
      return false
    }

    // 如果没有输入金额或金额无效，不需要 approve
    if (!this.isInputAmountValid) {
      return false
    }

    // 获取当前的授权数量
    const currentAllowance = this.selectedToken.allowanceForCashier.value

    // 获取用户输入的金额
    const inputAmount = this.inputAmountValue

    // 如果当前授权数量小于输入金额，则需要 approve
    return currentAllowance < inputAmount
  }
}
