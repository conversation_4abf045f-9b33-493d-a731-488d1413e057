import { makeAutoObservable } from 'mobx'
import { formatBalance, validateAmountInput } from '@/lib/utils'
import { formatUnits, parseUnits } from 'viem'

export class BigIntState {
  value: bigint = 0n
  decimal = 18
  loading: boolean = false
  private _amount: string = '0'
  private _rawInputAmount: string = '' // 保存用户原始输入，避免精度丢失

  constructor(args: Partial<BigIntState>) {
    Object.assign(this, args)
    makeAutoObservable(this)
    // 初始化时计算 amount
    this.updateAmount()
  }

  /**
   * 获取格式化的 amount
   * 当 loading 为 true 时返回 "..."
   * 如果有原始输入，优先返回原始输入以保持精度
   */
  get amount(): string {
    if (this.loading) {
      return '...'
    }

    // 如果有原始输入且有效，返回原始输入（用于输入场景）
    if (this._rawInputAmount && validateAmountInput(this._rawInputAmount)) {
      return this._rawInputAmount
    }

    // 否则返回格式化后的值（用于显示场景）
    return this._amount
  }

  /**
   * 设置 amount 值（用于用户输入）
   * 保留原始输入精度，避免格式化导致的精度丢失
   * @param amount - 用户输入的数值字符串
   * @param preserveInput - 是否保留原始输入，默认 true
   */
  setAmount(amount: string, preserveInput: boolean = true) {
    if (this.loading) return

    // 移除千分位分隔符
    const cleanAmount = amount.replace(/,/g, '')

    if (preserveInput && validateAmountInput(cleanAmount)) {
      // 保存原始输入，用于输入场景
      this._rawInputAmount = cleanAmount

      // 同时更新 value
      try {
        // 如果是空字符串，设置为 0
        if (cleanAmount === '') {
          this.value = 0n
        } else {
          this.value = parseUnits(cleanAmount, this.decimal)
        }
      } catch (error) {
        console.warn('Invalid amount format:', amount)
        this._rawInputAmount = ''
        return
      }
    } else {
      // 清除原始输入，使用格式化值
      this._rawInputAmount = ''
      this._amount = formatBalance(cleanAmount, {
        useThousandsSeparator: true,
      })

      // 同时更新 value
      try {
        this.value = parseUnits(cleanAmount, this.decimal)
      } catch (error) {
        console.warn('Invalid amount format:', amount)
      }
    }
  }

  /**
   * 设置原始输入金额（专门用于用户输入场景）
   * @param amount - 用户输入的原始金额字符串
   */
  setRawAmount(amount: string) {
    this.setAmount(amount, true)
  }

  /**
   * 设置格式化金额（用于显示场景）
   * @param amount - 要格式化显示的金额
   */
  setFormattedAmount(amount: string) {
    this.setAmount(amount, false)
  }

  /**
   * 设置 value 值并自动更新 amount
   * @param value - bigint 类型的值
   * @param clearRawInput - 是否清除原始输入，默认 true
   */
  setValue(value: bigint, clearRawInput: boolean = true) {
    this.value = value

    if (clearRawInput) {
      this._rawInputAmount = ''
    }

    this.updateAmount()
  }

  /**
   * 设置 decimal 位数并重新计算 amount
   * @param decimal - 小数位数
   */
  setDecimal(decimal: number) {
    this.decimal = decimal

    // 如果有原始输入，重新解析
    if (this._rawInputAmount) {
      try {
        this.value = parseUnits(this._rawInputAmount, this.decimal)
      } catch (error) {
        console.warn('Error re-parsing raw input with new decimal:', error)
        this._rawInputAmount = ''
      }
    }

    this.updateAmount()
  }

  /**
   * 设置 loading 状态
   * @param loading - 是否正在加载
   */
  setLoading(loading: boolean) {
    this.loading = loading
    // 当 loading 状态改变时，如果变为 false，需要重新计算 amount
    if (!loading) {
      this.updateAmount()
    }
  }

  /**
   * 清除原始输入，强制使用格式化显示
   */
  clearRawInput() {
    this._rawInputAmount = ''
  }

  /**
   * 使用 wagmi 的 formatUnits 更新 amount
   * @private
   */
  private updateAmount() {
    if (this.loading) return

    try {
      // 使用 wagmi 的 formatUnits 处理 actualValue
      const actualValue = formatUnits(this.value, this.decimal)

      // 使用 formatBalance 进行格式化，自动处理千分位
      this._amount = formatBalance(actualValue, {
        useThousandsSeparator: true,
      })
    } catch (error) {
      console.warn('Error updating amount:', error)
      this._amount = '0'
    }
  }

  /**
   * 获取原始的数值字符串（不带千分位格式化）
   */
  get rawAmount(): string {
    if (this.loading) {
      return '...'
    }

    try {
      return formatUnits(this.value, this.decimal)
    } catch (error) {
      return '0'
    }
  }

  /**
   * 获取带千分位格式化的 amount（强制格式化，不使用原始输入）
   */
  get formattedAmount(): string {
    if (this.loading) {
      return '...'
    }
    return this._amount
  }

  /**
   * 获取用户的原始输入（如果存在）
   */
  get rawInputAmount(): string {
    return this._rawInputAmount
  }

  /**
   * 检查是否有原始输入
   */
  get hasRawInput(): boolean {
    return this._rawInputAmount !== ''
  }

  /**
   * 获取用于显示的金额（优先使用原始输入）
   * 这个方法专门用于输入框显示
   */
  get displayAmount(): string {
    if (this.loading) {
      return '...'
    }

    // 如果有原始输入（包括空字符串），返回原始输入
    if (this._rawInputAmount !== '') {
      return this._rawInputAmount
    }

    // 如果没有原始输入且值为0，返回空字符串（用于输入框显示）
    if (this.value === 0n) {
      return ''
    }

    // 否则返回原始数值（不格式化）
    return this.rawAmount
  }
}
