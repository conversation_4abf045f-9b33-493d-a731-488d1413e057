import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Image } from '@heroui/react'
import { Network } from '@/types/network.ts'
import { useSwitchChain } from 'wagmi'
import { useState } from 'react'

export type NetworkSwitcherProps = {
  isOpen: boolean
  onOpenChange: () => void
  fromNetwork: Network
  toNetwork: Network
  onConfirm?: () => void
}

export function NetworkSwitcher(props: NetworkSwitcherProps) {
  const { switchChain, isPending } = useSwitchChain()
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await switchChain({ chainId: props.toNetwork.chainId })
      props.onConfirm?.()
      props.onOpenChange()
    } catch (error) {
      console.error('Network switch failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    props.onOpenChange()
  }

  return (
    <Modal
      className="max-w-sm"
      hideCloseButton={isLoading}
      isDismissable={!isLoading}
      isOpen={props.isOpen}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -10,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
      size="sm"
      onOpenChange={props.onOpenChange}
    >
      <ModalContent className="mx-3">
        {() => (
          <>
            <ModalHeader className="text-lg font-semibold text-color8 text-center pb-1 pt-4">
              Switch Network
            </ModalHeader>
            <ModalBody className="px-5 py-3">
              <div className="flex flex-col items-center space-y-4">
                <p className="text-color7 text-xs text-center leading-relaxed opacity-90">
                  Switch your wallet to continue
                </p>

                <div className="flex items-center justify-center w-full gap-6">
                  {/* From Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-color7 text-xs font-medium">From</span>
                    <div className="relative">
                      {/* 连接状态指示器 */}
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse" />
                      <div className="bg-color3 rounded-full p-2 border border-color9 shadow-sm transition-all duration-300 hover:scale-105">
                        <Image alt={props.fromNetwork.name} className="size-7" src={props.fromNetwork.logoUrl} />
                      </div>
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.fromNetwork.name}
                    </span>
                  </div>

                  {/* Network Switch Animation */}
                  <div className="flex flex-col items-center justify-center">
                    <div className="relative flex items-center">
                      {/* 数据流动效果 */}
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse" />
                        <div
                          className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse"
                          style={{ animationDelay: '0.2s' }}
                        />
                        <div
                          className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse"
                          style={{ animationDelay: '0.4s' }}
                        />
                      </div>

                      {/* 切换箭头 */}
                      <div className="mx-2 bg-purple1 rounded-full p-2 shadow-md">
                        <svg
                          className="size-4 text-white transform transition-transform duration-500 hover:translate-x-1"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            clipRule="evenodd"
                            d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                            fillRule="evenodd"
                          />
                        </svg>
                      </div>

                      {/* 数据流动效果 */}
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse"
                          style={{ animationDelay: '0.6s' }}
                        />
                        <div
                          className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse"
                          style={{ animationDelay: '0.8s' }}
                        />
                        <div
                          className="w-2 h-0.5 bg-purple1 rounded-full animate-pulse"
                          style={{ animationDelay: '1s' }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* To Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-purple1 text-xs font-medium">To</span>
                    <div className="relative">
                      {/* 等待连接状态 */}
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full border-2 border-white animate-bounce" />
                      {/* 目标网络呼吸效果 */}
                      <div className="absolute -inset-0.5 bg-purple1/20 rounded-full animate-pulse" />
                      <div className="relative bg-gradient-to-br from-purple1/10 to-purple1/20 rounded-full p-2 border-2 border-purple1 shadow-md transition-all duration-300 hover:scale-105">
                        <Image alt={props.toNetwork.name} className="size-7" src={props.toNetwork.logoUrl} />
                      </div>
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.toNetwork.name}
                    </span>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-center gap-3 px-5 pb-5 pt-2">
              <Button
                className="bg-color3 text-color8 rounded-full px-6 h-9 min-w-[90px] text-sm font-medium border border-color9 hover:bg-color9/20 transition-all duration-200"
                isDisabled={isLoading}
                variant="flat"
                onPress={handleCancel}
              >
                Cancel
              </Button>
              <Button
                className={`rounded-full px-6 h-9 min-w-[90px] text-sm font-medium shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${
                  isLoading || isPending
                    ? 'bg-purple1/80 text-white animate-pulse'
                    : 'bg-gradient-to-r from-purple1 to-purple2 text-white hover:shadow-lg hover:from-purple2 hover:to-purple1'
                }`}
                isDisabled={isLoading || isPending}
                isLoading={isLoading || isPending}
                onPress={handleConfirm}
              >
                {isLoading || isPending ? (
                  <span className="flex items-center gap-2">
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        fill="none"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor"
                      />
                    </svg>
                    Switching...
                  </span>
                ) : (
                  'Switch Network'
                )}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}
