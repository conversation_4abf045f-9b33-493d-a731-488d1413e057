import { Button } from '@heroui/button'
import { Link } from '@heroui/link'
import {
  Navbar as HeroUINavbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
} from '@heroui/navbar'
import { link as linkStyles } from '@heroui/theme'
import clsx from 'clsx'

import { siteConfig } from '@/config/site'
import ThemeSwitch from '@/components/ThemeSwitch'
import { GithubIcon } from '@/components/icons'
import { Image } from '@heroui/image'
import { useAppKit } from '@reown/appkit/react'
import { formatBalance } from '@/lib/utils'
import { networks } from '@/config/network.ts'
import { useBalance } from 'wagmi'
import { formatUnits } from 'viem'
import { useStore } from '@/store'

export const Navbar = () => {
  const { accountStore } = useStore()
  const { open } = useAppKit()
  const { data: balance } = useBalance({
    address: accountStore.walletAddress as `0x${string}`,
    query: {
      enabled: accountStore.isWalletConnected,
      refetchInterval: 30000,
    },
  })

  const network = networks.find(net => net.chainId === accountStore.currentChainId)

  function ConnectedUI() {
    return (
      <div className="flex gap-5">
        <Button
          className="rounded-full py-0 px-2 h-8 bg-color3"
          variant="solid"
          onPress={() => open({ view: 'Account' })}
        >
          <div className="flex items-center gap-3">
            <Image className="size-6" src={network?.logoUrl} />
            <div className="font-medium text-sm">{network?.name}</div>
            <Image src="/public/images/icon_dropdown.svg" />
          </div>
        </Button>
        <div className="flex items-center gap-2">
          <Image className="size-7 p-1 bg-color4" src={network?.logoUrl} />
          {balance && (
            <div className="text-sm font-medium text-purple1">
              {formatBalance(formatUnits(balance.value, 18))} {balance.symbol}
            </div>
          )}
          <div className="text-sm font-medium text-purple1 ml-10">{accountStore.getShortAddress()}</div>
        </div>
      </div>
    )
  }

  return (
    <>
      <HeroUINavbar className="bg-color1 px-6" maxWidth="full" position="sticky">
        <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
          <NavbarBrand className="gap-3 max-w-fit">
            <Link href="/">
              <Image className="rounded-none h-6" src="/public/images/logo_iotube.svg" />
            </Link>
          </NavbarBrand>
          <div className="hidden lg:flex gap-4 justify-start ml-2">
            {siteConfig.navItems.map((item: any) => (
              <NavbarItem key={item.href}>
                <Link
                  className={clsx(
                    linkStyles({ color: 'foreground' }),
                    'data-[active=true]:text-primary data-[active=true]:font-medium'
                  )}
                  color="foreground"
                  href={item?.href}
                >
                  {item?.label}
                </Link>
              </NavbarItem>
            ))}
          </div>
        </NavbarContent>

        <NavbarContent className=" sm:flex basis-1/5 sm:basis-full" justify="end">
          <NavbarItem className="sm:flex gap-5">
            {accountStore.isWalletConnected ? (
              <ConnectedUI />
            ) : (
              <Button className="rounded-full py-0 px-2 h-8 bg-color3" onPress={() => open({ view: 'Connect' })}>
                Connect
              </Button>
            )}
            <ThemeSwitch />
          </NavbarItem>
        </NavbarContent>

        <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
          <Link isExternal href={siteConfig.links.github}>
            <GithubIcon className="text-default-500" />
          </Link>
          <ThemeSwitch />
          <NavbarMenuToggle />
        </NavbarContent>

        <NavbarMenu>
          <div className="mx-4 mt-2 flex flex-col gap-2">
            {siteConfig.navMenuItems.map((item, index) => (
              <NavbarMenuItem key={`${item}-${index}`}>
                <Link
                  color={
                    index === 2 ? 'primary' : index === siteConfig.navMenuItems.length - 1 ? 'danger' : 'foreground'
                  }
                  href="#"
                  size="lg"
                >
                  {item.label}
                </Link>
              </NavbarMenuItem>
            ))}
          </div>
        </NavbarMenu>
      </HeroUINavbar>
    </>
  )
}
